<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Flights API</title>
</head>
<body>
    <h1>Test Flights API</h1>
    <button onclick="testFlights()">Load Flights</button>
    <div id="result"></div>

    <script>
        async function testFlights() {
            const resultDiv = document.getElementById('result');
            try {
                // First authenticate
                const authResponse = await fetch('http://localhost:8090/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin1', password: 'adminpass' })
                });
                
                if (!authResponse.ok) {
                    throw new Error('Auth failed: ' + await authResponse.text());
                }
                
                const authData = await authResponse.json();
                console.log('Auth data:', authData);
                
                // Then fetch flights
                const flightsResponse = await fetch('http://localhost:8090/flights', {
                    method: 'GET',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authData.token}`
                    }
                });
                
                if (!flightsResponse.ok) {
                    throw new Error('Flights failed: ' + await flightsResponse.text());
                }
                
                const flightsData = await flightsResponse.json();
                console.log('Flights data:', flightsData);
                resultDiv.innerHTML = '<pre>' + JSON.stringify(flightsData, null, 2) + '</pre>';
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
