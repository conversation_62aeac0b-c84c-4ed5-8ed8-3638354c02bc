import { useParams, useNavigate } from 'react-router-dom'
import { useState, useEffect } from 'react'
import * as passengerService from '../../../services/passengerService'
import * as flightService from '../../../services/flightService'
import Layout from '../../../components/Layout'
import Button from '../../../components/ui/Button'
import Input, { SearchInput } from '../../../components/ui/Input'
import Card, { CardHeader, CardTitle, CardDescription, CardBody } from '../../../components/ui/Card'
import Badge, { FlightStatusBadge } from '../../../components/ui/Badge'
import SeatMap from '../../../components/SeatMap'
import '../../../styles/demo.css'

export default function FlightDetails() {
  const { flightId } = useParams()
  const navigate = useNavigate()
  const [flight, setFlight] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [passengerFilter, setPassengerFilter] = useState('All')
  const [passengerList, setPassengerList] = useState([])
  const [seatMap, setSeatMap] = useState([])
  const [selectedSeats, setSelectedSeats] = useState([])
  const [unavailableSeats, setUnavailableSeats] = useState([])

  // Mock staff user for layout
  const staffUser = {
    username: 'staff1',
    role: 'checkinStaff',
    name: 'Check-in Staff'
  }

  useEffect(() => {
    let mounted = true
    async function load() {
      try {
        const f = await flightService.findById(Number(flightId))
        if (!mounted) return
        setFlight(f)
        setSeatMap(f?.seatMap || [])
        const ps = await passengerService.listByFlight(Number(flightId))
        if (!mounted) return
        setPassengerList(ps)

        // Update unavailable seats based on passenger assignments
        const unavailable = ps
          .filter(p => p.seat && p.checkedIn)
          .map(p => p.seat)
          .filter(seat => seat)
        setUnavailableSeats(unavailable)
      } catch (err) {
        console.error('Failed to load flight or passengers', err)
      }
    }
    load()
    return () => { mounted = false }
  }, [flightId])

  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
  }

  const handleFilterChange = (e) => {
    setPassengerFilter(e.target.value)
  }

  const handleFilter = (type) => {
  setPassengerFilter(type)
  }

  const filteredPassengers = passengerList.filter((p) => {
    const matchesSearch = (p.name || '').toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter =
      passengerFilter === 'Checked In' ? p.checkedIn :
      passengerFilter === 'Not Checked In' ? !p.checkedIn :
      passengerFilter === 'All' ? true : true
    return matchesSearch && matchesFilter
  })

  const handleSeatChange = (passengerId, newSeat) => {
    // Deprecated: keep for compatibility
    setPassengerList((prev) => prev.map((p) => (p.id === passengerId ? { ...p, seat: newSeat } : p)))
  }

  const handleAssignSeat = (passengerId, newSeatNumber) => {
    // Compute updated passenger list deterministically then update seatMap based on it
    const updatedPassengers = passengerList.map((p) =>
      p.id === passengerId ? { ...p, seat: newSeatNumber ? String(newSeatNumber) : '' } : p
    )

    setPassengerList(updatedPassengers)

    // compute booked seats from updated list
    const booked = new Set()
    updatedPassengers.forEach((p) => {
      const n = parseInt((p.seat || '').toString().match(/^\d+/)?.[0], 10)
      if (!isNaN(n)) booked.add(n)
    })

    setSeatMap((prevMap) => prevMap.map((s) => ({ ...s, isBooked: booked.has(s.number) })))

    // Update unavailable seats for SeatMap component
    const unavailable = updatedPassengers
      .filter(p => p.seat && p.checkedIn)
      .map(p => p.seat)
      .filter(seat => seat)
    setUnavailableSeats(unavailable)
  }

  const handleCheckin = (passengerId) => {
    const updated = passengerList.map((p) => (p.id === passengerId ? { ...p, checkedIn: true } : p))
    setPassengerList(updated)
    // don't change seatMap yet; seats are still unassigned until staff picks one
  }

  const handleBack = () => {
    navigate('/staff/check-in')
  }

  const handleSeatSelect = (seatId, selectedSeats) => {
    setSelectedSeats(selectedSeats)
    console.log('Seat selected:', seatId)
    console.log('All selected seats:', selectedSeats)
  }

  return (
    <Layout title="Flight Check-In Details" showNavigation={true} user={staffUser}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            ← Back to Check-In Management
          </Button>
        </div>

        {!flight ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="ml-4 text-neutral-600">Loading flight information...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Flight Information Card */}
            <Card>
              <CardHeader>
                <CardTitle>Flight {flight.name || flight.id}</CardTitle>
                <CardDescription>
                  {flight.route} • {flight.date} • {flight.aircraftType}
                </CardDescription>
              </CardHeader>
              <CardBody>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-neutral-500 mb-1">Flight Details</h3>
                    <p className="text-sm"><strong>Flight ID:</strong> {flight.id}</p>
                    <p className="text-sm"><strong>Aircraft Type:</strong> {flight.aircraftType}</p>
                    <p className="text-sm"><strong>Route:</strong> {flight.route}</p>
                    <p className="text-sm"><strong>Date:</strong> {flight.date}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-neutral-500 mb-1">Capacity</h3>
                    <p className="text-sm"><strong>Total Seats:</strong> {flight.totalSeats}</p>
                    <p className="text-sm"><strong>Available:</strong> {flight.availableSeats}</p>
                    <p className="text-sm"><strong>Passengers:</strong> {passengerList.length}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-neutral-500 mb-1">Status</h3>
                    <FlightStatusBadge status={flight.status || 'scheduled'} />
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Services Card */}
            {flight?.services && flight.services.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Available Services</CardTitle>
                  <CardDescription>Services offered on this flight</CardDescription>
                </CardHeader>
                <CardBody>
                  <div className="flex flex-wrap gap-2">
                    {flight.services.map((service) => (
                      <Badge key={service} variant="secondary">
                        {service}
                      </Badge>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Passenger Management Card */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Passenger Management</CardTitle>
                    <CardDescription>
                      {filteredPassengers.length} passenger{filteredPassengers.length !== 1 ? 's' : ''}
                      {passengerList.length > 0 && ` (${passengerList.length} total)`}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardBody>
                <div className="mb-6 flex flex-col sm:flex-row gap-4">
                  <SearchInput
                    placeholder="Search passengers..."
                    value={searchTerm}
                    onChange={handleSearch}
                    className="flex-1"
                  />
                  <select
                    value={passengerFilter}
                    onChange={handleFilterChange}
                    className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="All">All Passengers</option>
                    <option value="Checked In">Checked In</option>
                    <option value="Not Checked In">Not Checked In</option>
                  </select>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-neutral-200">
                        <th className="text-left py-3 px-4 font-medium text-neutral-700">Name</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-700">Services</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-700">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-neutral-700">Seat Assignment</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredPassengers.map((passenger) => (
                        <tr key={passenger.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                          <td className="py-3 px-4">
                            <button
                              className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
                              onClick={() => navigate(`/staff/check-in/${flight.id}/${passenger.name}`)}
                            >
                              {passenger.name}
                            </button>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex flex-wrap gap-1">
                              {(passenger.services || []).map((service, index) => (
                                <Badge key={index} variant="outline" size="sm">
                                  {service}
                                </Badge>
                              ))}
                              {(!passenger.services || passenger.services.length === 0) && (
                                <span className="text-neutral-500 text-sm">None</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            {passenger.checkedIn ? (
                              <Badge variant="success">Checked In</Badge>
                            ) : (
                              <Badge variant="warning">Pending</Badge>
                            )}
                          </td>
                          <td className="py-3 px-4">
                            {passenger.checkedIn ? (
                              <select
                                value={passenger.seat || ''}
                                onChange={(e) => handleAssignSeat(passenger.id, e.target.value ? parseInt(e.target.value, 10) : '')}
                                className="px-3 py-1 border border-neutral-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              >
                                <option value="">Select Seat</option>
                                {seatMap
                                  .filter((s) => !s.isBooked || (passenger.seat && parseInt((passenger.seat || '').toString().match(/^\d+/)?.[0], 10) === s.number))
                                  .map((s) => (
                                    <option key={s.number} value={s.number}>
                                      Seat {s.number}
                                    </option>
                                  ))}
                              </select>
                            ) : (
                              <Button
                                onClick={() => handleCheckin(passenger.id)}
                                variant="primary"
                                size="sm"
                              >
                                Check In
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardBody>
            </Card>

            {/* Seat Map Card */}
            <Card>
              <CardHeader>
                <CardTitle>Aircraft Seat Map</CardTitle>
                <CardDescription>
                  Interactive seat map for flight {flight?.name || flight?.id}
                </CardDescription>
              </CardHeader>
              <CardBody>
                <SeatMap
                  rows={10}
                  seatsPerRow={6}
                  unavailableSeats={unavailableSeats}
                  selectedSeats={selectedSeats}
                  onSeatSelect={handleSeatSelect}
                  showLegend={true}
                />
              </CardBody>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  )
}
